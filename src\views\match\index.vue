<template>
    <div class="match-container">
        <!-- 顶部导航栏 -->
        <div class="nav-tabs">
            <div class="leftNav">
                <img @click="onClickLeft" src="@/assets/images/match/back.webp" alt="">
                <span>{{ $t('match.matchTitle') }}</span>
            </div>
            <div class="centerNav">
                <Tab :data="tabs" type="match" @changeTab="changeTab"></Tab>
            </div>
            <div class="rightNav" @click="gotoDetail">
                <div class="myMatch">
                    <span>{{ $t('match.myMatch') }}</span>
                </div>
            </div>
        </div>

        <!-- 二级导航 - 使用SubTab组件 -->
        <SubTab :tabs="subTabs" v-model:activeTab="activeTab" @tab-click="onClicksubTab" />

        <!-- 内容区域 - 使用MatchList组件 -->
        <MatchList ref="matchListRef" :active-tab="activeTab" :main-tab="currentMainTab" @scroll-position-saved="onScrollPositionSaved" />
        
        <!-- 全局加载状态 -->
        <div v-if="isInitialLoading" class="global-loading">
            <van-loading color="#1989fa" size="60" />
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed, defineAsyncComponent, onActivated, onDeactivated } from 'vue';
const Tab = defineAsyncComponent(() =>
    import('@/components/index/tab.vue')
);
import MatchList from '@/components/match/matchList.vue';
import SubTab from '@/components/match/subTab.vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import matchService from '@/services/matchService';
import { routeHistory } from '@/router'; // 导入路由历史记录

const router = useRouter();
const route = useRoute();
import emitter from '@/utils/mitt';
const tabs = ref([]);
const subTabs = ref([{ id: 1, title: '待开始' }, { id: 2, title: '进行中' }, { id: 3, title: '已结束' }]);
const activeTab = ref(0); // 默认选中"进行中"
const currentMainTab = ref(null);
const matchListRef = ref(null);
const isInitialLoading = ref(false);

// 存储滚动位置
const savedScrollPosition = ref(0);

// 接收子组件保存的滚动位置
const onScrollPositionSaved = (position) => {
    savedScrollPosition.value = position;
    console.log('父组件接收到滚动位置:', position);
};

// 初始化数据
const initializeData = async () => {
    // 如果服务已初始化，则直接使用缓存数据
    if (matchService.state.initialized) {
        const state = matchService.getCurrentState();
        tabs.value = state.tabs;
        currentMainTab.value = state.currentMainTab;
        activeTab.value = state.currentSubTab;
        return;
    }
    
    // 否则显示加载状态并初始化数据
    isInitialLoading.value = true;
    
    try {
        // 加载标签数据
        const tabsData = await matchService.fetchMatchTabs();
        tabs.value = tabsData;
        
        // 设置默认选中的标签
        if (tabsData.length > 0) {
            currentMainTab.value = tabsData[0].id;
            
            // 通知 MatchList 组件重新布局
            nextTick(() => {
                if (matchListRef.value) {
                    matchListRef.value.relayoutMasonry();
                }
            });
        }
    } catch (error) {
        console.error('初始化 Match 数据失败:', error);
        showToast('加载数据失败，请重试');
    } finally {
        isInitialLoading.value = false;
    }
};

// 切换顶部主标签
const changeTab = (index) => {
    console.log('切换主标签:', index);
    
    // 设置当前选中的主标签ID
    if (tabs.value && tabs.value[index]) {
        currentMainTab.value = tabs.value[index].id;
        
        // 将二级标签重置为"待开始"（索引为0）
        activeTab.value = 0;
        
        // 更新服务中的状态，同时更新主标签和二级标签
        matchService.setCurrentTabs(currentMainTab.value, activeTab.value);
    }
    
    // Tab切换后通知子组件重新布局
    nextTick(() => {
        if (matchListRef.value) {
            matchListRef.value.relayoutMasonry();
        }
    });
};

// 切换二级tab
const onClicksubTab = (index) => {
    console.log('父组件接收到二级标签点击:', index);
    
    // 更新服务中的状态
    matchService.setCurrentTabs(undefined, index);
    
    // 标签切换后通知子组件重新布局
    if (matchListRef.value) {
        matchListRef.value.relayoutMasonry();
    }
};

// 进入我的比赛
const gotoDetail = () => {
    router.push({
        path: '/myMatch',
    });
};

// 返回上一页
const onClickLeft = () => {
    console.log('返回按钮点击');

    // 检查路由历史记录，查找上一个有效路由
    let previousRoute = '';
    for (let i = routeHistory.length - 1; i >= 0; i--) {
        if (routeHistory[i] && routeHistory[i] !== '/match') {
            previousRoute = routeHistory[i];
            break;
        }
    }

    console.log('上一个有效路由:', previousRoute);

    // 如果上一个有效路由是首页或根路径，或者没有找到有效的上一个路由，则发送移除缓存事件
    if (previousRoute === '/index' || previousRoute === '/' || !previousRoute) {
        console.log('检测到返回到首页，发送移除match缓存事件');
        // 发送移除match缓存的事件，让Layout组件处理缓存移除和状态清理
        emitter.emit('removeMatchCache');
    }

    router.back();
};

// 监听从详情页返回的事件
const handleFromDetail = () => {
    console.log('从详情页返回');

    // 使用子组件的方法恢复滚动位置
    if (matchListRef.value) {
        matchListRef.value.restoreScrollPosition(savedScrollPosition.value);
    }
};

// 监听路由变化
watch(() => route.path, (newPath, oldPath) => {
    if (newPath === '/match' && oldPath && oldPath.includes('/matchDetail')) {
        handleFromDetail();
    }
}, { immediate: true });

// 监听openMatch事件
emitter.on('openMatch', () => {
    router.push({
        path: '/matchDetail',
        query: {
            id: '1'
        }
    });
});

// 在组件挂载后初始化
onMounted(() => {
    initializeData();
});

onActivated(() => {
    console.log('match页面激活');

    // 检查matchService是否有缓存数据，如果没有说明是全新进入，需要重新初始化
    const currentState = matchService.getCurrentState();
    const hasCache = Object.keys(currentState.matchLists).length > 0 || Object.keys(currentState.myMatchLists).length > 0;

    if (!hasCache) {
        console.log('检测到无缓存数据，重新初始化match页面');
        // 重置页面状态为初始值
        activeTab.value = 0;
        currentMainTab.value = null;
        savedScrollPosition.value = 0;
        isInitialLoading.value = false;

        // 重新初始化数据
        initializeData();
    } else {
        console.log('检测到有缓存数据，恢复滚动位置');
        // 恢复滚动位置
        if (matchListRef.value) {
            matchListRef.value.restoreScrollPosition(savedScrollPosition.value);
        }
    }
});

// 组件失活时的处理
onDeactivated(() => {
    console.log('match页面失活');
    // 保存当前滚动位置
    if (matchListRef.value) {
        savedScrollPosition.value = matchListRef.value.getCurrentScrollPosition();
        console.log('保存match页面滚动位置:', savedScrollPosition.value);
    }
});

// 组件卸载时清理
onUnmounted(() => {
    console.log('match页面组件卸载');

    // 移除事件监听
    emitter.off('openMatch');
});
</script>

<style lang="scss" scoped>
.match-container {
    background: linear-gradient(180deg, #EEEFF2 0%, #E7F1FF 100%);
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden; // 防止整体溢出
    position: relative; // 添加相对定位
}

// 全局加载状态
.global-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 999;
}

// 导航标签样式
.nav-tabs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-bottom: 22px;
    margin-top: 30px;
    flex-shrink: 0; // 防止导航栏被压缩
    position: relative;
    z-index: 10;

    .leftNav {
        display: flex;
        align-items: center;
        margin: 0 109px 0 30px;

        img {
            width: 40px;
            height: 40px;
        }

        span {
            margin-left: 8px;
            font-size: 26px;
            color: rgba(51, 51, 51, 0.8);
        }
    }

    .rightNav {
        color: #333333;
        font-size: 24px;
        margin-right: 78px;
    }
}
</style>