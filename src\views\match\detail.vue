<template>
    <div class="match-detail-container">
        <!-- 顶部导航栏 -->
        <van-nav-bar class="custom-nav" :border="false">
            <template #left>
                <img class="back-icon" @click="onClickLeft" src="@/assets/images/match/back.webp" alt="">
            </template>
            <template #title>
                <span>{{ matchDetail.title }}</span>
            </template>
            <template #right>
                <div class="status" :class="{
                    'status-waiting': matchDetail.status == 0,
                    'status-progress': matchDetail.status == 2,
                    'status-finished': matchDetail.status == 3
                }">
                    <div>
                        {{ statusText }}
                    </div>
                </div>
            </template>
        </van-nav-bar>
        <div class="match-detail-container-content">
            <!-- 横幅图片 -->
            <div class="banner-container">
                <!-- fit="contain" -->
                <van-image :src="matchDetail.coverImage" class="banner-image" fit="contain" />
                <div class="affix" @click="addClick">
                    <img src="@/assets/images/index/add.webp" />
                </div>
            </div>
            <!-- 标签导航栏 -->
            <div class="tabs-container">
                <van-tabs v-model:active="activeTab" :sticky="false" swipeable animated line-height="0" :border="false"
                    @click-tab="onClickTab">
                    <van-tab v-for="(tab, index) in tabs" :key="index" :title="tab.title">
                        <!-- 内容区域 -->
                        <div class="content-container">
                            <div class="content-text" v-if="index === 0">
                                <Rule :title="$t('match.matchCon')" :data="matchDetail.content" />
                            </div>
                            <div class="content-text" v-else-if="index === 1">
                                <Rule :title="$t('match.matchRule')" :data="matchDetail.rule" />
                            </div>
                            <div class="content-text" v-else-if="index === 2">
                                <Reward :title="$t('match.matchAward')" :firstPlaceReward="matchDetail.firstPlaceReward"
                                    :secondPlaceReward="matchDetail.secondPlaceReward"
                                    :thirdPlaceReward="matchDetail.thirdPlaceReward" />
                            </div>
                            <div class="content-text" v-else-if="index === 3">
                                <Rule :title="$t('match.matchInstructions')" :data="matchDetail.instructions" />
                            </div>
                            <div class="works-container" v-else-if="index === 4">
                                <WorkList :matchId="matchId" ref="workListRef"
                                    @scroll-position-saved="onScrollPositionSaved">
                                </WorkList>
                            </div>
                        </div>
                    </van-tab>
                </van-tabs>
            </div>
        </div>
        <div v-if="dialogFormAdd">
            <Add :dialogFormAdd="dialogFormAdd" :id="matchId" :type="uploadType" @dialogCloseAdd="dialogCloseAdd"
                @addSuccess="addSuccess">
            </Add>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, defineAsyncComponent, onActivated, onDeactivated, nextTick, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import emitter from '@/utils/mitt';
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import { getMatchDetail, hasUploadWorks } from '@/api/match';

// 显式定义组件名称，以便与keep-alive的include列表匹配
defineOptions({
    name: 'matchDetail'
});
const Add = defineAsyncComponent(() =>
    import('@/components/match/add.vue')
);
const Rule = defineAsyncComponent(() =>
    import('@/components/match/rule.vue')
);
const Reward = defineAsyncComponent(() =>
    import('@/components/match/reward.vue')
);
const WaterfallList = defineAsyncComponent(() =>
    import('@/components/index/waterfallList.vue')
);
const WorkList = defineAsyncComponent(() =>
    import('@/components/match/workList.vue')
);
const router = useRouter();
const route = useRoute();
const dialogFormAdd = ref(false)
// 获取路由参数
const matchId = ref(route.query.id || 1);
const uploadType = ref('')
// 标签页状态
const activeTab = ref(0);

// 文字截断函数
const truncateText = (text, maxLength = 6) => {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

// 原始标签数据
const originalTabs = [
    { title: t('match.matchCon'), id: 1 },
    { title: t('match.matchRule'), id: 2 },
    { title: t('match.matchAward'), id: 3 },
    { title: t('match.matchInstructions'), id: 4 },
    { title: t('match.matchWorks'), id: 5 }
];

// 处理后的标签数据（可选择是否启用文字截断）
const tabs = ref(originalTabs.map(tab => ({
    ...tab,
    // 如果需要截断文字，取消下面这行的注释
    // title: truncateText(tab.title, 6)
})));

// 数据加载状态
const loading = ref(false);
const error = ref(null);
const matchDetail = ref({});
// 添加一个标志来跟踪初始加载
const initialLoadCompleted = ref(false);

// 缓存页面状态
const pageState = ref({
    activeTab: 0,
    scrollPosition: 0
});

// 添加 statusText 计算属性
const statusText = computed(() => {
    switch (matchDetail.value.status) {
        case '0': return t('match.statusWaiting');
        case '2': return t('match.statusProgress');
        case '3': return t('match.statusEnd');
        default: return t('match.statusEnd');
    }
});

// 获取比赛详情数据
const getMatchDetailData = async () => {
    // 如果已经在加载中，则不重复请求
    if (loading.value) return;

    loading.value = true;
    error.value = null;

    try {
        console.log('获取比赛详情，参数:', matchId.value);
        const res = await getMatchDetail(matchId.value);

        if (res.code === 200 && res.data) {
            console.log('比赛详情数据:', res.data);
            matchDetail.value = res.data;
            uploadType.value = res.data.categoryType;
            // 更新页面标题
            document.querySelector('.van-nav-bar__title span').textContent = res.data.title;
            // 标记初始加载已完成
            initialLoadCompleted.value = true;
        } else {
            console.error('获取比赛详情失败:', res.msg);
        }
    } catch (err) {
        console.error('获取比赛详情异常:', err);
    } finally {
        loading.value = false;
    }
};

// 添加缺失的addClick方法
const addClick = async () => {
    // 判断是否有资格上传作品
    const res = await hasUploadWorks({
        contestId: matchId.value,
        userId: countStore.loginData.roleId
    })
    if (res.code == 200) {
        console.log('添加按钮点击');
        dialogFormAdd.value = true
    } else {
        showToast(t('match.againAddWork'))
    }
};
//  关闭add
const dialogCloseAdd = (data) => {
    dialogFormAdd.value = data
}// add添加或修改成功
const addSuccess = () => {
    // 通知workList组件更新列表
    emitter.emit('refurbishWorkMyList')


}
// 清理资源，释放内存
const cleanupResources = () => {
    console.log('清理matchDetail页面资源');
    // 释放可能占用内存的大型数据
    matchDetail.value = {};
    // 重置初始加载标志
    initialLoadCompleted.value = false;

    // 重置状态
    pageState.value = { activeTab: 0, scrollPosition: 0 }
};

// 返回上一页，避免使用window.location导致整页刷新
const onClickLeft = () => {
    console.log('返回按钮点击');

    // 判断是否返回到match页面，如果是则进行资源清理
    // 由于无法直接获取上一页的路径，我们将发送一个事件通知Layout组件更新缓存
    console.log('即将返回，通知移除matchDetail缓存');
    emitter.emit('removeMatchDetailCache');

    // 保存当前状态以便可能的下次恢复
    savePageState();

    // 先返回上一页
    router.back();

    // 使用延时执行，避免路由导航与原生通信冲突
    setTimeout(() => {
        if (countStore.deviceType == 'Web') {
        } else {
            // 通知打开横屏
            // window.location.href = "uniwebview://changeToLandscape";
        }
    }, 100);
};

// 保存页面状态
const savePageState = () => {
    const contentContainer = document.querySelector('.content-container');
    if (contentContainer) {
        pageState.value = {
            activeTab: activeTab.value,
            scrollPosition: contentContainer.scrollTop
        };
        console.log('保存页面状态:', pageState.value);
    }
};

// 恢复页面状态
const restorePageState = () => {
    console.log('恢复页面状态:', pageState.value);
    activeTab.value = pageState.value.activeTab;

    // 使用nextTick确保DOM已更新后再设置滚动位置
    // nextTick(() => {
    //     const contentContainer = document.querySelector('.content-container');
    //     if (contentContainer) {
    //         contentContainer.scrollTop = pageState.value.scrollPosition;
    //         console.log('已恢复滚动位置:', pageState.value.scrollPosition);
    //     }
    // });
};

emitter.on('backMatch', () => {
    router.back();
})

// 切换标签页
const onClickTab = (index) => {
    console.log(activeTab.value)
    console.log('点击标签页:', index);
    // 保存当前选中的标签页
    pageState.value.activeTab = activeTab.value;
    // 如果标签页是作品页，则通知workList组件更新列表
    if (activeTab.value == 4) {
        // console.log('通知workList组件更新列表')
        emitter.emit('refurbishWorkOtherList')
    }
};

// 在script setup部分添加对WorkList组件的引用
const workListRef = ref(null);

// 监听滚动位置保存事件
const onScrollPositionSaved = (position) => {
    console.log('接收到WorkList组件保存的滚动位置:', position);
    // 可以在这里进行额外处理，如果需要的话
};

// 页面加载时获取比赛详情
onMounted(() => {
    console.log('比赛ID:', matchId.value);
    console.log('matchDetail组件已挂载');
    // 获取比赛详情数据
    getMatchDetailData();
});

// 当组件从缓存中被激活时调用
onActivated(() => {
    console.log('matchDetail页面激活，组件name:', 'matchDetail');

    // 检查是否从视频页面返回
    const fromVideoToMatch = localStorage.getItem('fromVideoToMatch');

    // 从缓存恢复时，恢复保存的状态
    restorePageState();

    if (fromVideoToMatch === 'true') {
        console.log('检测到从视频页面返回，准备恢复WorkList滚动位置');

        // 使用nextTick确保DOM已更新
        nextTick(() => {
            // 确保当前标签页是作品页
            if (activeTab.value !== 4) {
                console.log('当前不在作品标签页，切换到作品标签页');
                activeTab.value = 4;
            }

            // 再次使用nextTick确保标签页切换后DOM已更新
            nextTick(() => {
                // 如果WorkList组件已挂载，调用其重新布局方法
                if (workListRef.value) {
                    console.log('调用WorkList组件的重新布局方法');
                    workListRef.value.relayoutMasonry();
                }
            });
        });
    } else if (!initialLoadCompleted.value) {
        // 只有在不是首次加载、不在加载中且数据为空的情况下才重新获取
        console.log('数据为空，重新获取比赛详情');
        getMatchDetailData();
    }
});

// 当组件被缓存时调用
onDeactivated(() => {
    console.log('matchDetail页面失活，保存状态');
    // 保存当前页面状态，以便下次激活时恢复
    savePageState();
});

onUnmounted(() => {
    console.log('matchDetail组件卸载');
    // 确保清理所有资源
    cleanupResources();
    // 移除事件监听
    emitter.off('backMatch');
});
</script>

<style lang="scss" scoped>
.match-detail-container {
    // background-color: #f5f7fa;
    // min-height: 100vh;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    // 自定义导航栏样式
    .custom-nav {
        background-color: #ffffff;

        .back-icon {
            width: 40px;
            height: 40px;
        }

        .status {
            width: 150px;
            height: 46px;
            border-radius: 4px;
            background: #CFE7FF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #4DA6FF;
        }

        .status-waiting {
            background: rgba(255, 168, 56, 0.18);
            color: #FFAC41;
        }

        .status-progress {
            background: #CFE7FF;
            color: #4DA6FF;
        }

        .status-finished {
            background: #E1E1E1;
            color: #8C8C8C;
        }

        :deep(.van-nav-bar__content) {
            height: 100px;
        }

        :deep(.van-nav-bar__left) {
            font-size: 28px;
        }

        :deep(.van-nav-bar__title) {
            font-size: 28px;
            font-weight: 500;
            color: rgba(51, 51, 51, 0.9);
        }

        :deep(.van-nav-bar__title) {
            display: flex;
            align-items: center;
            height: 100%;
        }
    }

    .match-detail-container-content {
        flex: 1;
        overflow: auto;
        // padding: 0 78px;
        padding: 0 38px;
        display: flex;
        flex-direction: column;

        // 横幅图片样式
        .banner-container {
            width: 100%;
            height: 300px;
            // height: 40%;
            // overflow: hidden;
            position: relative;

            .banner-image {
                width: 100%;
                height: 100%;
            }

            .affix {
                position: absolute;
                right: 0;
                bottom: 0;

                img {
                    width: 108px;
                    height: 108px;
                }
            }
        }

        // 标签导航栏样式
        .tabs-container {
            background: #DAE5F7;
            // flex: 1;
            // display: flex;
            // flex-direction: column;
            // overflow: hidden;

            // 加载中状态样式
            .loading-container {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                height: 100%;
                background: #F1F5FC;

                p {
                    margin-top: 20px;
                    font-size: 26px;
                    color: #666;
                }
            }

            // 错误状态样式
            .error-container {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                height: 100%;
                background: #F1F5FC;

                p {
                    margin-bottom: 20px;
                    font-size: 26px;
                    color: #ff4d4f;
                }

                .van-button {
                    height: 60px;
                    font-size: 26px;
                    padding: 0 30px;
                    background-color: #4DA6FF;
                    border-color: #4DA6FF;
                }
            }

            :deep(.van-tabs__wrap) {
                border-bottom: 1px solid #f0f0f0;
                min-height: 88px; // 改为最小高度，允许自适应
                height: auto; // 高度自适应
                flex-shrink: 0;
                overflow: visible; // 允许内容显示
            }

            :deep(.van-tabs__nav) {
                display: flex;
                width: 100%;
                min-height: 88px; // 最小高度
                height: auto; // 高度自适应
                align-items: stretch; // 让所有标签高度一致
            }

            :deep(.van-tabs) {
                height: 100%;
                display: flex;
                flex-direction: column;
            }

            :deep(.van-tab) {
                // font-size: 28px;
                font-size: 22px;
                color: #666666;
                background: #DAE5F7;
                padding: 8px 4px; // 上下增加padding，左右减少
                min-width: 0; // 允许flex收缩
                flex: 1; // 平均分配宽度
                max-width: none; // 移除最大宽度限制
                height: auto; // 允许高度自适应
                min-height: 88px; // 设置最小高度
                display: flex; // 使用flex布局
                align-items: center; // 垂直居中
                justify-content: center; // 水平居中
            }

            // 允许标题换行显示
            :deep(.van-tab__text) {
                white-space: normal; // 允许换行
                word-break: break-word; // 在单词边界换行，避免单词被截断
                word-wrap: break-word; // 兼容性
                text-align: center; // 文字居中
                line-height: 1.3; // 设置行高
                display: block; // 块级元素
                width: 100%; // 占满容器宽度
                hyphens: auto; // 自动断字（如果支持）
            }

            :deep(.van-tab--active) {
                font-weight: 500;
                // font-size: 28px;
                font-size: 22px;
                color: #FFFFFF;
                background: #4DA6FF;

                // 激活状态下也保持换行样式
                .van-tab__text {
                    color: #FFFFFF;
                }
            }

            // :deep(.van-tabs__line) {
            //     background-color: #2970ff;
            // }

            :deep(.van-tabs__content) {
                flex: 1;
                overflow: hidden;
                position: relative;
            }


            :deep(.van-tab__panel) {
                height: 100%;
                overflow: hidden;
            }

            :deep(.van-tabs__wrap) {
                // margin: 0 92px;
                margin: 0 10px;
            }
        }

        :deep(.van-tabs__content--animated) {
            height: 100%;
        }

        :deep(.van-tabs__nav--line) {
            padding-bottom: 0;
        }

        // 内容区域样式
        .content-container {
            height: 100%;
            background: #F1F5FC;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            position: relative;

            &::after {
                content: '';
                display: block;
                height: 1px;
                opacity: 0.01;
                margin-bottom: -1px;
            }

            .content-text {
                margin: 60px 94px;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
                font-size: 26px;
                line-height: 40px;
                color: #474747;
                white-space: pre-wrap;

                &:nth-child(1),
                &:nth-child(2),
                &:nth-child(3),
                &:nth-child(4) {
                    height: auto;
                }
            }

            .works-container {
                height: 100%;
                // display: flex;
                // justify-content: center;
                // align-items: center;
                // min-height: 200px;

            }
        }
    }
}

// 悬浮按钮样式
.float-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 99;

    :deep(.van-button) {
        background-color: #2970ff;
        border-color: #2970ff;
        font-size: 14px;
        padding: 0 20px;
        height: 40px;
        box-shadow: 0 2px 8px rgba(41, 112, 255, 0.3);
    }
}
</style>
