<template>
  <router-view v-slot="{ Component }">
    <keep-alive :include="cachedComponents">
      <component :is="Component" />
    </keep-alive>
  </router-view>

</template>
<script>
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import emitter from '@/utils/mitt';
import matchService from '@/services/matchService';

export default {
  setup() {
    const route = useRoute();
    const router = useRouter();

    // 需要缓存的组件名称列表
    const cachedComponents = ref(['index', 'home', 'reportOne', 'reportTwo', 'reportEnd', 'videoPage','match','matchDetail']);

    // 检查组件名称是否与缓存列表匹配
    const checkComponentName = (component) => {
      if (component && component.type && component.type.name) {
        console.log('当前组件名称:', component.type.name);
        if (!cachedComponents.value.includes(component.type.name)) {
          console.warn('警告: 组件名称不在缓存列表中:', component.type.name);
        }
      } else {
        console.warn('警告: 无法获取组件名称');
      }
    };

    // 移除matchDetail缓存的函数
    const removeMatchDetailCache = () => {
      console.log('接收到移除matchDetail缓存的请求');
      const index = cachedComponents.value.indexOf('matchDetail');
      if (index !== -1) {
        cachedComponents.value.splice(index, 1);
        console.log('已从缓存列表移除matchDetail');
      }
    };

    // 移除videoPage缓存的函数
    const removeVideoPageCache = () => {
      console.log('接收到移除videoPage缓存的请求');
      const index = cachedComponents.value.indexOf('videoPage');
      if (index !== -1) {
        cachedComponents.value.splice(index, 1);
        console.log('已从缓存列表移除videoPage');
      }
    };

    // 移除match缓存的函数
    const removeMatchCache = () => {
      console.log('接收到移除match缓存的请求');
      const index = cachedComponents.value.indexOf('match');
      if (index !== -1) {
        cachedComponents.value.splice(index, 1);
        console.log('已从缓存列表移除match');

        // 清理matchService缓存数据，确保下次进入是全新状态
        matchService.clearCache();
        console.log('已清理matchService缓存数据');
      }
    };

    // 监听removeMatchDetailCache事件
    emitter.on('removeMatchDetailCache', removeMatchDetailCache);
    
    // 监听removeVideoPageCache事件
    emitter.on('removeVideoPageCache', removeVideoPageCache);
    
    // 监听removeMatchCache事件
    emitter.on('removeMatchCache', removeMatchCache);

    onMounted(() => {
      console.log('Layout组件挂载，初始缓存组件列表:', cachedComponents.value);
      // 打印当前路由路径
      console.log('当前路由路径:', route.path);
    });

    // 在组件卸载时移除事件监听
    onUnmounted(() => {
      emitter.off('removeMatchDetailCache', removeMatchDetailCache);
      emitter.off('removeVideoPageCache', removeVideoPageCache);
      emitter.off('removeMatchCache', removeMatchCache);
    });

    // 监听路由变化，动态控制组件缓存
    watch(() => route.path, (newPath, oldPath) => {
      console.log('路由变化:', oldPath, '->', newPath);
      console.log('路由变化前缓存组件列表:', [...cachedComponents.value]);

      // 从matchDetail页面跳转到match页面时，移除matchDetail的缓存
      if (oldPath === '/matchDetail' && newPath === '/match') {
        const index = cachedComponents.value.indexOf('matchDetail');
        if (index !== -1) {
          cachedComponents.value.splice(index, 1);
          console.log('从matchDetail页面跳转到match页面，已从缓存列表移除matchDetail');
        }
      }

      // 从match页面跳转到index页面时，移除match的缓存
      if (oldPath === '/match' && newPath === '/index') {
        const index = cachedComponents.value.indexOf('match');
        if (index !== -1) {
          cachedComponents.value.splice(index, 1);
          console.log('从match页面跳转到index页面，已从缓存列表移除match');

          // 清理matchService缓存数据，确保下次进入是全新状态
          matchService.clearCache();
          console.log('已清理matchService缓存数据');
        }
      }

      // 从任何页面进入video页面时，添加缓存
      if (newPath === '/video' && !cachedComponents.value.includes('videoPage')) {
        cachedComponents.value.push('videoPage');
        console.log('进入video页面，添加videoPage到缓存列表');
      }

      // 从video页面进入除index外的任何页面时，保持缓存
      if (oldPath === '/video' && newPath !== '/index') {
        if (!cachedComponents.value.includes('videoPage')) {
          cachedComponents.value.push('videoPage');
          console.log('从video页面进入非index页面，添加videoPage到缓存列表');
        } else {
          console.log('videoPage已在缓存列表中');
        }
      }
      // 当从video页面回到index页面时，移除视频页面缓存
      if (newPath === '/index' && oldPath === '/video') {
        const index = cachedComponents.value.indexOf('videoPage');
        if (index !== -1) {
          cachedComponents.value.splice(index, 1);
          console.log('从video页面回到index页面，从缓存列表移除videoPage');
        }
      }

      // 如果matchDetail不在缓存列表中且进入matchDetail页面，则添加到缓存列表
      if (newPath === '/matchDetail' && !cachedComponents.value.includes('matchDetail')) {
        cachedComponents.value.push('matchDetail');
        console.log('进入matchDetail页面，添加matchDetail到缓存列表');
      }

      // 如果match不在缓存列表中且进入match页面，则添加到缓存列表
      if (newPath === '/match' && !cachedComponents.value.includes('match')) {
        cachedComponents.value.push('match');
        console.log('进入match页面，添加match到缓存列表');
      }

      // 打印当前缓存的组件列表，用于调试
      console.log('当前缓存组件列表:', cachedComponents.value);
    }, { immediate: true });

    return {
      cachedComponents
    };
  }
}
</script>
<style scoped></style>
